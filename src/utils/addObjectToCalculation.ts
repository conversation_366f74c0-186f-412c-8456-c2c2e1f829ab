import { knowledgeStore } from '@/store'
import { useAppMode } from '@/store/useAppMode'
import KnowledgeModal from '@calculation/knowledge/KnowledgeModal.vue'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import type { DetailSearchContentItem } from '@domain/masterdata/model/Detail'
import { Header } from '@domain/masterdata/model/Header'
import { useWizardCalculationStore } from '@domain/wizard/wizardCalculation.store'
import DetailSelect from '@masterdata/detail-select/DetailSelect.vue'
import type { DetailsFilterState } from '@masterdata/details-filter/DetailsFilter.model'
import { $t } from '@shared/translation/nuTranslation'
import {
  CalculationCreationModalMode,
  CalculationPosition,
} from '@tset/shared-model/calculation/WizardCalculationEnums'
import {
  masterdataConfigurations,
  masterdataEntityTypesInCost,
} from '@tset/shared-model/masterdata/NuMasterdata'
import type { PAGE } from '@tset/shared-model/navigation/navigation'
import { newField } from '@tset/shared-utils/helpers/manufacturing'
import Notifications from '@tset/shared-utils/plugins/notifications'
import { useModal } from '@tset/shared-utils/plugins/vue-modal'
import { h } from 'vue'

export type Input = {
  type: ManufacturingEntityType
  loadedManufacturingId?: string
  targetPage?: PAGE
  /**
   * Only used for masterdata, so we don't prefix it with MD_ or anything.
   */
  parentType?: string
  parentId?: string
  entityClass?: string
  // for sub calculation
  prefillStep?: { id: string; displayDesignation: string }

  // entity linking
  linkEntityId?: string
  entityClassFilters?: string[]
  tabsToShow?: SelectedKnowledgeTab[]
  linkEntityField?: string
}

const { currentMode, fromAppMode } = useAppMode()
const wizardCalculationStore = useWizardCalculationStore()

const { show } = useModal()

const entityClassMap: Partial<Record<ManufacturingEntityType, string>> = {
  C_PART: 'ElectronicComponent',
}

/**
 * Used to trigger the add flow to add an object like a material,
 * consumable, manufacturing step, ... to a calculation
 *
 * The loadedManufacturingId is only needed for the special direct cost type.
 *
 * @param {Input} input
 * @returns {void}
 */
export async function addObjectToCalculation(input: Input) {
  if (masterdataEntityTypesInCost.includes(input.type)) {
    addNewMasterdataMaterial({
      ...input,
      entityClass: input.entityClass ?? entityClassMap[input.type],
    })
    return
  }

  let modalResult: Awaited<ReturnType<typeof knowledgeStore.startAddObject>> =
    undefined

  switch (input.type) {
    case 'MATERIAL':
    case 'MANUFACTURING_STEP':
      modalResult = await knowledgeStore.startAddObject({
        entityType: input.type,
        entityClassFilters: input.entityClassFilters,
        tabsToShow: input.tabsToShow,
        linkEntityId: input.linkEntityId,
        linkEntityField: input.linkEntityField,
      })
      break
    case 'SPECIAL_DIRECT_COST': {
      if (!input.loadedManufacturingId) {
        throw new Error(
          'loadedManufacturingId is required for the special direct cost type'
        )
      }
      modalResult = await knowledgeStore.startAddObject({
        entityType: input.type,
        entityClassFilters: input.entityClassFilters,
        parentId: input.loadedManufacturingId,
        tabsToShow: input.tabsToShow,
        linkEntityId: input.linkEntityId,
        linkEntityField: input.linkEntityField,
      })
      break
    }
    case 'BOM_ENTRY':
      wizardCalculationStore
        .startAddCalculation({
          calculationMode: CalculationCreationModalMode.NEW,
          calculationPosition: CalculationPosition.SUB,
          targetPage: input.targetPage,
        })
        .then(() => {
          if (input.prefillStep) {
            wizardCalculationStore.prefillTheStep(
              input.prefillStep.id,
              input.prefillStep.displayDesignation
            )
          }
        })
      break
    default:
      throw new Error(`unhandled object type - ${input.type}`)
  }

  if (modalResult?.modalProps) {
    await show({
      component: KnowledgeModal,
      props: modalResult.modalProps,
    })
  }
}

export function addSubCalculationToStep(stepId: string) {
  wizardCalculationStore.setParentStepId(stepId)
  addObjectToCalculation({ type: 'BOM_ENTRY' })
}

export function addNewMasterdataMaterial(input: Input) {
  const type = input.type as (typeof masterdataEntityTypesInCost)[number]

  const configuration = masterdataConfigurations[type]
  if (!configuration) {
    console.error(`no configuration found for masterdata entity type ${type}`)
    return
  }

  const headerTypeField = manufacturingStore.loadedManufacturing?.getField(
    configuration.headerTypeId
  )
  const classificationTypeField =
    manufacturingStore.loadedManufacturing?.getField(
      configuration.classificationTypeId
    )
  const classificationKey = manufacturingStore.loadedManufacturing?.getField(
    configuration.classificationId
  )?.value

  if (!classificationKey || !headerTypeField || !classificationTypeField) {
    Notifications.error({
      title: $t('notifications.title.anErrorOccuredWithoutRequest'),
      message: $t('notifications.error.anErrorOccuredWithoutRequest'),
    })
    return
  }

  knowledgeStore.startAddObject(
    {
      entityType: type,
      parentId: input.parentId ?? input.loadedManufacturingId,
      parentType: 'MD_MATERIAL_PARENT',
      entityClass: input.entityClass,
      // entity linking
      linkEntityId: input.linkEntityId,
      entityClassFilters: input.entityClassFilters,
      tabsToShow: input.tabsToShow,
      linkEntityField: input.linkEntityField,
    },
    true
  )

  const appModeFilter: DetailsFilterState['filters'] = {
    _BUILTIN_detailValueType: [
      {
        type: 'builtinlov',
        equals: fromAppMode('price', 'emission'),
      },
    ],
  }

  useModal().show({
    component: KnowledgeModal,
    props: {
      projectId: knowledgeStore.projectId,
      modalName: 'knowledgeModal',
    },
    slots: {
      nuMasterdataSelect: (slotArgs) => [
        h(DetailSelect, {
          key: 'material-select',
          headerTypeKey: headerTypeField.value as string,
          technology: slotArgs?.calculationModuleTechnology,
          classificationConfig: generatedClassificationConfig(
            classificationTypeField.value as string,
            classificationKey as string,
            slotArgs?.calculationModuleTechnology as string
          ),
          filters: appModeFilter,
          onSelect: onNewMasterdataSelect,
        }),
      ],
    },
  })
}

/**
 * This function is called when a new masterdata material is selected in the add modal (KnowledgeModal).
 */
export async function onNewMasterdataSelect(item: DetailSearchContentItem) {
  const { headerDto } = item

  // Get the assigned classification in the material classificationType
  const headerClassification =
    headerDto.classifications?.['tset.ref.classification-type.material']?.[0]
      .key

  if (!headerClassification) {
    throw new Error('headerClassification not found in selected item')
  }

  // change the selected Masterdata Item to fetch all the relevant fields from the BE
  let selectedItem: AddObjectRequest | undefined
  try {
    // The call can throw an error if there's a problem with the BE
    // or the selected item is not supported
    selectedItem = await knowledgeStore.changeSelectedMasterdataItem({
      headerKey: headerDto.key,
      headerClassificationKey: headerClassification,
    })
    knowledgeStore.setFetchNewMasterdataFieldsError(null)
  } catch (e) {
    // in case of an error, we set the fetchFieldsResponse to null,
    // to indicate the fields could not be fetched
    knowledgeStore.setFetchNewMasterdataFieldsError(String(e))
  }

  // without the selectedItem response from the BE, we cannot proceed
  if (!selectedItem) {
    return
  }

  /**
   * First we set the headerKey of the selected item
   * as a field named "headerKey" in the knowledgeStore.
   */
  const newMasterdataFields: ResultField[] = [
    newField('headerKey', headerDto.key, 'Text', undefined, undefined, 'I'),
    newField(
      'displayDesignation',
      headerDto.name ?? headerDto.key,
      'Text',
      undefined,
      undefined,
      'I'
    ),
  ]

  /**
   * For the modularized technologies, we need to add
   * the technology classification keys from the header.
   */
  const technologyClassificationType =
    headerDto.classifications?.['tset.ref.classification-type.technology']
  if (technologyClassificationType?.length) {
    newMasterdataFields.push(
      newField(
        'technologyClassifications',
        technologyClassificationType.map((c) => c.key),
        'Text',
        {
          multiple: true,
        }
      )
    )
  }

  // Get all the fields that should be passed through to the new masterdata material
  const passThroughFieldIds = ['stepId']
  const passThroughFields = selectedItem.fields.filter((field) =>
    passThroughFieldIds.includes(field.name)
  )

  newMasterdataFields.push(...passThroughFields)

  // Get the base currency of the item
  const baseCurrencyField = selectedItem.fields.find(
    (field) => field.name === 'baseCurrency'
  )
  const baseCurrency = getMasterdataItemBaseCurrency(item)
  if (baseCurrency && baseCurrency !== baseCurrencyField?.value) {
    newMasterdataFields.push(
      newField(
        'baseCurrency',
        baseCurrency,
        'Currency',
        undefined,
        undefined,
        'I'
      )
    )
  }

  /**
   * To comply with the existing knowledge logic on the backend we need to do:
   * 1. Compare the value from the resultField (backend) and the value from the selectedItem (frontend)
   * 2. Overwrite the corresponding value field (cost / co2) if they are different
   */
  const overwrittenValueField = getDetailValue(
    item,
    selectedItem,
    currentMode.value,
    baseCurrency as Currency
  )
  if (overwrittenValueField) {
    newMasterdataFields.push(overwrittenValueField)
  }

  // add all the fields to the knowledgeStore
  knowledgeStore.setFieldsMasterdata(newMasterdataFields)

  // Update the loading state, because there's nothing to wait for loading
  knowledgeStore.setLoadingState({ name: 'canClick', state: true })
}

// This function should proably live somewhere else, but where?
// We should anyway restructure this code in the next step with electronic components.
function getMasterdataItemBaseCurrency(
  item: DetailSearchContentItem
): string | undefined {
  const { detail, headerDto } = item

  // Check detail value currency
  const detailCurrency = detail?.getValueCurrency()
  if (detailCurrency) {
    return detailCurrency
  }

  // Check header detail value schema currency
  const headerPriceCurrency = new Header({
    ...headerDto,
    classifications: undefined,
  }).getValueTypePriceCurrency()

  if (headerPriceCurrency) {
    return headerPriceCurrency
  }

  return undefined
}

/**
 * Gets the value and compares it to the lookup value. Returns the value to set as the value field.
 * @param {DetailSearchContentItem} item
 * @returns The value to set as the value field. Undefined if not value should be set.
 */
export function getDetailValue(
  item: DetailSearchContentItem,
  selectedItem: AddObjectRequest,
  appMode: Mode,
  baseCurrency?: Currency
): ResultField | undefined {
  if (
    item.detail?.value.type !== 'numeric' ||
    item.detail?.value.valueInBaseSiUnit === undefined
  ) {
    return undefined
  }

  const lookupField = selectedItem.fields.find(
    (field) =>
      field.name === fromAppMode('materialBasePrice', 'materialBaseCO2')
  )

  if (!lookupField) {
    return undefined
  }

  // the target we need to compare based on the appMode
  const detailValueTypeTargetKey = appMode ? 'price' : 'emission'

  // get the reference field value
  let lookupValue: number | undefined = lookupField.value as number
  if (appMode === 'cost' && baseCurrency && lookupField.currencyInfo) {
    lookupValue = lookupField.currencyInfo?.[baseCurrency]
  }

  const masterdataBaseSiUnitValue = item.detail.value.valueInBaseSiUnit

  if (!lookupValue) {
    return undefined
  }

  // get the diff of the lookup value and the item value
  const diff = lookupValue - masterdataBaseSiUnitValue
  const threshold = 0.00000001
  const isDifferenceAboveThreshold = Math.abs(diff) > threshold

  // if the detail matches the target and the values are different, we overwrite the value
  if (
    item.detail.detailValueTypeKey === detailValueTypeTargetKey &&
    isDifferenceAboveThreshold
  ) {
    return {
      ...lookupField,
      source: 'I',
      value: masterdataBaseSiUnitValue,
    }
  }

  return undefined
}

function generatedClassificationConfig(
  classificationType: string,
  classificationKey: string,
  technology?: string
): {
  classificationTypeKey: string
  classificationKeys: string[]
}[] {
  const classifications = [
    {
      classificationTypeKey: classificationType,
      classificationKeys: classificationKey.split(','),
    },
  ]
  if (technology) {
    classifications.push({
      classificationTypeKey: 'tset.ref.classification-type.technology',
      classificationKeys: [technology],
    })
  }
  return classifications
}
